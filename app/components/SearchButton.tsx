import React, { useEffect, useMemo, useState } from 'react';
import SearchIcon from "@/app/images/searchIcon.svg";
import clsx from 'clsx';
import { checkSearch, getDefaultSearchEngineConfig } from '../[workspace]/admin/search/actions';
import { useParams } from 'next/navigation';
import to from '../utils/await-to';
import { Button, ButtonProps, Dropdown, Flex, MenuProps } from 'antd';
import useModelListStore from '../store/modelList';
import useChatStore, { WebSearchType } from '../store/chat';
import { useTranslations } from 'next-intl';
import { CheckOutlined } from '@ant-design/icons';

interface SearchButtonProps {
  searchEnable: boolean;
  localSearchEnable: boolean;
  onToggle: (opened?: boolean, key?: WebSearchType) => void;
  buttonProps?: ButtonProps
}

const SearchButton: React.FC<SearchButtonProps> = ({
  searchEnable,
  localSearchEnable,
  onToggle,
  buttonProps
}) => {
  const t = useTranslations('Chat');
  const {workspace} = useParams()
  const { currentModel } = useModelListStore();
  const { webSearchType, setWebSearchType } = useChatStore()
  const [searchEngine, setSearchEngine] = useState<any>()
  const { builtInWebSearch } = currentModel
  const items = useMemo(() => {
    const options = [{
      key: WebSearchType.None,
      label: '关闭'
    }, {
      key: WebSearchType.BuiltIn,
      label: '模型内置'
    }, {
      key: WebSearchType.ThirdParty,
      label: searchEngine?.name
    }]
    return options.map(option => {
      const checked = webSearchType === option.key
      return {
        key: option.key,
        label: (
          <div className="flex items-center justify-between min-w-20">
            <span>{option.label}</span>
            { checked && (
              <CheckOutlined className="text-blue-500 ml-2" style={{ fontSize: '12px' }} />
            )}
          </div>
        ),
        className: checked ? 'bg-blue-50' : '',
      }
    })
  }, [searchEngine, webSearchType])
  const isActive = webSearchType !== WebSearchType.None

  useEffect(() => {
    const getSearchEngine = async(workspace: string) => {
      const configResult = await getDefaultSearchEngineConfig(workspace);
      if (configResult.status === 'success') {
        setSearchEngine(configResult.data);
      }
    }

    if (workspace) {
      getSearchEngine(workspace as string)
    }
  }, [workspace])

  useEffect(() => {
    if (currentModel && !currentModel?.builtInWebSearch) {
      setWebSearchType(WebSearchType.None)
    }
  }, [currentModel, setWebSearchType])

  useEffect(() => {
    if (currentModel) {
      if (!currentModel.builtInWebSearch && webSearchType === WebSearchType.BuiltIn) {
        onToggle(false, WebSearchType.None)
      }
    }
  }, [currentModel, searchEnable, webSearchType, onToggle])

  if (!searchEnable && !builtInWebSearch) {
    return <div></div>;
  }

  if (searchEngine && builtInWebSearch && searchEnable) {
    return <Dropdown placement='topLeft' menu={{items, onClick: ({key}) => {
       onToggle(key !== WebSearchType.None, key as WebSearchType)
    }}}>
      {!buttonProps ? <div
        className={clsx('flex h-7 flex-row items-center pr-3 pl-2 search-button-custom py-1 cursor-pointer rounded-2xl border',
          {
            'bg-blue-100 border-blue-400 text-blue-700 hover:bg-blue-100': isActive,
            'hover:bg-gray-100 text-gray-500': !isActive
          })}
      >
        <SearchIcon style={{ fontSize: '16px'}} />
        <span className='text-xs ml-1'>联网</span>
      </div> :
      <Button {...buttonProps}>
        <Flex className={clsx({
          'text-gray-500': !isActive
        })}>
          <SearchIcon style={{ fontSize: '16px'}} />
          <span className='text-xs ml-1' style={{letterSpacing: 'normal'}}>{t('webSearch')}</span>
        </Flex>
      </Button>}
    </Dropdown>
  }

  return !buttonProps ?
    <div
      className={clsx('flex h-7 flex-row items-center pr-3 pl-2 search-button-custom py-1 cursor-pointer rounded-2xl border',
        {
          'bg-blue-100 border-blue-400 text-blue-700 hover:bg-blue-100': isActive,
          'hover:bg-gray-100 text-gray-500': !isActive
        })}
      onClick={() => {
        let key = searchEnable ? WebSearchType.ThirdParty : WebSearchType.BuiltIn
        if (isActive) {
          key = WebSearchType.None
        }
        onToggle(!isActive, key)
      }}
    >
      <SearchIcon style={{ fontSize: '16px'}} />
      <span className='text-xs ml-1'>联网</span>
    </div> :
    <Button
      {...buttonProps}
      className='ml-1'
      onClick={() => {
        let key = searchEnable ? WebSearchType.ThirdParty : WebSearchType.BuiltIn
        if (isActive) {
          key = WebSearchType.None
        }
        onToggle(!isActive, key)
      }}
    >
      <Flex className={clsx({
          'text-gray-500': !isActive
        })}>
        <SearchIcon style={{ fontSize: '16px'}} />
        <span className='text-xs' style={{letterSpacing: 'normal'}}>{t('webSearch')}</span>
      </Flex>
    </Button>
};

export default SearchButton;