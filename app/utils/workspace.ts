'use server';
import { db } from '@/app/db';
import { auth } from "@/auth";
import { eq, and } from 'drizzle-orm';
import { userWorkspace } from '@/app/db/schema';

/**
 * 验证用户是否有权限访问指定的 workspace
 * @param workspaceId - workspace ID
 * @param userId - 用户 ID（可选，如果不提供则从 session 获取）
 * @returns Promise<{hasAccess: boolean, userId?: string, role?: string}>
 */
export async function validateWorkspaceAccess(workspaceId: string, userId?: string) {
  try {
    // 如果没有提供 userId，从 session 获取
    if (!userId) {
      const session = await auth();
      if (!session?.user.id) {
        return {
          hasAccess: false,
          error: 'User not authenticated'
        };
      }
      userId = session.user.id;
    }

    // 检查用户是否有权限访问该 workspace
    const userWorkspaceRecord = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.userId, userId),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      )
    });
    if (!userWorkspaceRecord) {
      return {
        hasAccess: false,
        error: 'Workspace not found or access denied'
      };
    }

    return {
      hasAccess: true,
      userId,
      role: userWorkspaceRecord.role
    };
  } catch (error) {
    console.error('Workspace access validation error:', error);
    return {
      hasAccess: false,
      error: 'Internal server error'
    };
  }
}

/**
 * 获取当前用户的 session 和 workspace 访问权限
 * @param workspaceId - workspace ID
 * @returns Promise<{session: any, hasAccess: boolean, userId?: string, role?: string, error?: string}>
 */
export async function getSessionAndValidateWorkspace(workspaceId: string) {
  const session = await auth();

  if (!session?.user.id) {
    return {
      session: null,
      hasAccess: false,
      error: 'User not authenticated'
    };
  }

  const validation = await validateWorkspaceAccess(workspaceId, session.user.id);

  return {
    session,
    hasAccess: validation.hasAccess,
    userId: validation.userId,
    role: validation.role,
    error: validation.error
  };
}

/**
 * 标准的错误响应格式
 */
export const createErrorResponse = (message: string) => ({
  status: 'fail',
  message
});

/**
 * 标准的成功响应格式
 */
export const createSuccessResponse = (data?: any) => ({
  status: 'success',
  ...(data && { data })
});

/**
 * 从 Server Action 的 FormData 中获取 workspaceId 并验证权限
 * @param formData - FormData 对象
 * @returns Promise<{workspaceId: string, userId: string, role: string} | {error: string}>
 */
export async function getWorkspaceFromFormData(formData: FormData) {
  const workspaceId = formData.get('workspaceId') as string;

  if (!workspaceId) {
    return createErrorResponse('Workspace ID is required');
  }

  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  return {
    workspaceId,
    userId: validation.userId!,
    role: validation.role!
  };
}

export async function workspaceValidation(workspaceId: string) {
  const session = await auth();
  if (!session?.user.id) {
    return createErrorResponse('Access denied');
  }
  const userId = session.user.id;
  const userWorkspaceRecord = await db.query.userWorkspace.findFirst({
    where: and(
      eq(userWorkspace.userId, userId),
      eq(userWorkspace.workspaceId, workspaceId),
      eq(userWorkspace.isActive, true)
    )
  });
  if (!userWorkspaceRecord) {
    return createErrorResponse('Access denied');
  }
  return {
    status: 'success',
    workspaceId,
    userId: userId,
    role: userWorkspaceRecord.role
  };
}

/**
 * 从路径参数中获取 workspaceId 并验证权限
 * 用于页面组件中获取 workspace 参数
 * @param workspaceId - 从路由参数中获取的 workspace ID
 * @returns Promise<{workspaceId: string, userId: string, role: string} | {error: string}>
 */
export async function getWorkspaceFromParams(workspaceId: string) {
  if (!workspaceId) {
    return createErrorResponse('Workspace ID is required');
  }

  const validation = await getSessionAndValidateWorkspace(workspaceId);

  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  return {
    workspaceId,
    userId: validation.userId!,
    role: validation.role!
  };
}

/**
 * 验证用户是否为指定workspace的管理员
 * @param workspaceId - workspace ID
 * @param userId - 用户 ID（可选，如果不提供则从 session 获取）
 * @returns Promise<{isAdmin: boolean, userId: string, error?: string}>
 */
export async function isWorkspaceAdmin(workspaceId: string, userId?: string) {
  try {
    if (!userId) {
      const session = await auth();
      if (!session?.user.id) {
        return false;
      }
      userId = session.user.id;
    }
    const userWorkspaceRecord = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.userId, userId),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      )
    });
    if (!userWorkspaceRecord) {
      return {
        isAdmin: false,
        userId: '',
        error: 'Access denied'
      };
    }

    // 检查用户角色是否为 owner 或 admin
    const isAdmin = userWorkspaceRecord.role === 'owner' || userWorkspaceRecord.role === 'admin';

    return isAdmin;
  } catch (error) {
    console.error('Workspace admin validation error:', error);
    return false;
  }
}