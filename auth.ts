import NextAuth from "next-auth";
import { ZodError } from "zod";
import Credentials from "next-auth/providers/credentials";
import Google from "next-auth/providers/google"
import GitHub from "next-auth/providers/github"
import { signInSchema } from "@/lib/zod";
import { verifyPassword } from "@/app/utils/password";
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';
import { linkUserToPendingWorkspaces } from '@/app/[workspace]/admin/users/list/actions';

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    Google,
    GitHub,
    Credentials({
      // You can specify which fields should be submitted, by adding keys to the `credentials` object.
      // e.g. domain, username, password, 2FA token, etc.
      credentials: {
        email: {},
        password: {},
      },
      authorize: async (credentials) => {
        try {
          const { email, password } = await signInSchema.parseAsync(credentials);
          const user = await db.query.users
            .findFirst({
              where: eq(users.email, email)
            })
          if (!user || !user.password) {
            return null;
          }
          const passwordMatch = await verifyPassword(password, user.password);
          if (passwordMatch) {
            return {
              id: user.id,
              name: user.name,
              email: user.email,
            };
          } else {
            return null;
          }
        } catch (error) {
          if (error instanceof ZodError) {
            // 如果验证失败，返回 null 表示凭据无效
            return null;
          }
          // 处理其他错误
          throw error;
        }
      },
    }),

  ],
  pages: {
    error: '/auth/error', // 自定义错误页面
  },
  callbacks: {
    async redirect({ url, baseUrl }) {
      // 优化重定向逻辑，减少不必要的中间跳转
      if (url.startsWith("/")) {
        // 相对路径，直接使用
        return `${baseUrl}${url}`;
      } else if (new URL(url).origin === baseUrl) {
        // 同域名，直接使用
        return url;
      }
      // 默认跳转到根页面
      return baseUrl;
    },
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
      }
      if (account?.provider === "credentials" && token.sub) {
        token.provider = 'credentials';
      }
      if (account?.provider === "dingding" && token.sub) {
        const dbUser = await db.query.users.findFirst({
          where: eq(users.dingdingUnionId, account.providerAccountId)
        });

        if (dbUser) {
          token.id = dbUser.id;
        }
        token.provider = 'dingding';
      }
      if (account?.provider === "github" && token.sub) {
        // Github 登录时，使用 email 查找用户
        const dbUser = await db.query.users.findFirst({
          where: eq(users.email, token.email as string)
        });

        if (dbUser) {
          token.id = dbUser.id;
        } else {
          // 如果用户不存在，创建新用户
          const newUser = await db.insert(users).values({
            email: token.email as string,
            name: token.name as string,
            image: token.picture as string,
          }).returning();

          if (newUser[0]) {
            token.id = newUser[0].id;
            // 自动关联到待定的workspace记录
            await linkUserToPendingWorkspaces(newUser[0].id, token.email as string);
          }
        }
        token.provider = 'github';
      }
      if (account?.provider === "google" && token.sub) {
        // Google 登录时，使用 email 查找用户
        const dbUser = await db.query.users.findFirst({
          where: eq(users.email, token.email as string)
        });

        if (dbUser) {
          token.id = dbUser.id;
        } else {
          // 如果用户不存在，创建新用户
          const newUser = await db.insert(users).values({
            email: token.email as string,
            name: token.name as string,
            image: token.picture as string,
          }).returning();

          if (newUser[0]) {
            token.id = newUser[0].id;
            // 自动关联到待定的workspace记录
            await linkUserToPendingWorkspaces(newUser[0].id, token.email as string);
          }
        }
        token.provider = 'google';
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user = {
          ...session.user, // 保留已有的属性
          id: String(token.id),
          provider: token.provider as string,
        };
      }
      return session;
    },
  },
})