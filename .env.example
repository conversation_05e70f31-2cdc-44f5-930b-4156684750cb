NEXT_TELEMETRY_DISABLED=1
# PostgreSQL 数据库连接 URL，需本地安装或连接远程 PostgreSQL
# Docker 部署时无需填写
DATABASE_URL=
# 用于用户信息等敏感信息的加密，可以使用 openssl rand -base64 32 生成一个随机的 32 位字符串作为密钥。
# 以下为示例，生产环境请重新生成
AUTH_SECRET=PKqQmr74pyUXLR18kx85is9yXguIinaJ40DrOBim+Tg=
# 本地开发时，用于访问服务器的端口，生产环境请结合 nginx 等反向代理设置
HOST_PORT=3000
# 生产环境设置为正式域名
NEXTAUTH_URL=http://localhost:${HOST_PORT}
AUTH_TRUST_HOST=true

# 是否开启 Github 登录，开启值设为 ON，关闭时修改为 OFF
GITHUB_CLIENT_ID="your_github_client_id"
GITHUB_CLIENT_SECRET="your_github_client_secret"

# 是否开启 Google 登录，开启值设为 ON，关闭时修改为 OFF
GOOGLE_CLIENT_ID="your_google_client_id"
GOOGLE_CLIENT_SECRET="your_google_client_secret"

# AMPLITUDE 监测的 API Key
NEXT_PUBLIC_AMPLITUDE_API_KEY=xxxxx